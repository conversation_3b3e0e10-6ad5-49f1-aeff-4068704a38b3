import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '../../components/Button';
import FormBuilder from '../../components/form/FormBuilder';

const fields = [
  { name: 'title', label: 'Title', type: 'text', required: true, fullWidth: true },
  { name: 'language', label: 'Language', type: 'text', required: true, fullWidth: true },
  { name: 'region', label: 'Region', type: 'text', required: true, fullWidth: true },
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    required: true,
    fullWidth: true,
    options: [
      { label: 'Active', value: 'Active' },
      { label: 'Inactive', value: 'Inactive' },
    ],
  },
];

const ProjectFormModal = ({ open, onClose, onSubmit, initialValues, mode }) => (
  <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth sx={{ borderRadius: 0 }}>
    <DialogTitle
      sx={{
        background: '#43a047', // match Persona modal green
        color: '#fff',
        fontWeight: 600,
        fontSize: 22,
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        py: 2,
        px: 3,
      }}
    >
      {mode === 'edit' ? 'Edit Project' : 'Add New Project'}
    </DialogTitle>
    <DialogContent sx={{ p: 0 }}>
      <FormBuilder
        fields={fields}
        initialValues={initialValues}
        onSubmit={onSubmit}
        submitButtonText={mode === 'edit' ? 'Update' : 'Create'}
        cancelButtonText='Cancel'
        onCancel={onClose}
        formLayout={{
          fieldSpacing: 3,
          direction: 'column',
          variant: 'standard',
          padding: 3,
          buttonAlignment: 'flex-end',
          submitButtonProps: {
            variant: 'contained',
            color: 'primary',
            size: 'large',
            sx: { px: 4, ml: 2 },
          },
          cancelButtonProps: {
            variant: 'outlined',
            color: 'inherit',
            size: 'large',
            sx: { px: 4 },
          },
        }}
        layout='vertical'
        sx={{
          p: 3,
          '& .MuiFormControl-root': { mb: 4 },
          '& .MuiInputBase-root': { borderRadius: 0, mt: 1 },
          '& .MuiFormLabel-root': { borderRadius: 0, fontSize: '1rem', fontWeight: 500 },
          '& .MuiInputBase-input': { py: 1.5 },
        }}
      />
    </DialogContent>
  </Dialog>
);

export default ProjectFormModal;
