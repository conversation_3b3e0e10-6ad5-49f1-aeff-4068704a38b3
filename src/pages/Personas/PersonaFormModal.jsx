import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import { useTheme } from '@mui/material/styles';
import FormBuilder from '../../components/form/FormBuilder';

const CustomAIVoiceType = {
  PUCK: 'Puck',
  CHARON: 'Charon',
  KORE: 'Kore',
  FENRIR: 'Fenrir',
  AODE: 'Aoede',
  LEDA: 'Leda',
  ORUS: 'Orus',
  ZEPHYR: 'Zephyr',
};

const PersonaEngine = {
  CUSTOM: 'Custom',
  BLAND: 'Bland',
};

const PersonaStatus = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  ARCHIVED: 'Archived',
  TESTING: 'Testing',
  INACTIVE: 'Inactive',
};

const getFields = (mode) => {
  if (mode === 'edit') {
    return [
      {
        name: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        gridProps: { xs: 12 },
        size: 'large',
      },
      {
        name: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        options: Object.entries(PersonaStatus).map(([key, value]) => ({ label: value, value })),
        gridProps: { xs: 12 },
        size: 'large',
      },
      {
        name: 'is_default',
        label: 'Set as Default',
        type: 'switch',
        required: false,
        gridProps: { xs: 12 },
      },
    ];
  }

  // Create mode fields
  return [
    {
      name: 'name',
      label: 'Name',
      type: 'text',
      required: true,
      gridProps: { xs: 12 },
      size: 'large',
    },
    {
      name: 'voice_type',
      label: 'Voice Type',
      type: 'select',
      required: true,
      options: Object.entries(CustomAIVoiceType).map(([key, value]) => ({ label: value, value })),
      gridProps: { xs: 12 },
      size: 'large',
    },
    {
      name: 'engine',
      label: 'Engine',
      type: 'select',
      required: true,
      options: Object.entries(PersonaEngine).map(([key, value]) => ({ label: value, value })),
      gridProps: { xs: 12 },
      size: 'large',
    },
    {
      name: 'language',
      label: 'Language',
      type: 'select',
      required: true,
      options: [
        { label: 'English', value: 'English' },
        { label: 'Hindi', value: 'Hindi' },
        { label: 'Tamil', value: 'Tamil' },
        { label: 'Telugu', value: 'Telugu' },
        { label: 'Kannada', value: 'Kannada' },
      ],
      gridProps: { xs: 12 },
      size: 'large',
    },
    {
      name: 'from_number',
      label: 'Phone Number',
      type: 'text',
      required: true,
      placeholder: 'Enter phone number',
      gridProps: { xs: 12 },
      size: 'large',
    },
  ];
};

const PersonaFormModal = ({ open, onClose, onSubmit, initialValues, mode }) => {
  const theme = useTheme();
  const fields = getFields(mode);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        elevation: 1,
        sx: {
          borderRadius: 0,
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          py: 1.5,
          px: 3,
          fontSize: '1.25rem',
          fontWeight: 600,
          lineHeight: 1.6,
        }}
      >
        {mode === 'edit' ? 'Edit Persona' : 'Add New Persona'}
      </DialogTitle>
      <DialogContent sx={{ p: 3, pt: 2 }}>
        <FormBuilder
          fields={fields}
          initialValues={initialValues}
          onSubmit={onSubmit}
          formLayout={{
            fieldSpacing: 1.5, // Reduced from 3 to 1.5 (12px)
            direction: 'column',
            variant: 'standard',
            padding: 0,
            buttonAlignment: 'flex-end',
            submitButtonProps: {
              variant: 'contained',
              color: 'primary',
              size: 'large',
              sx: {
                px: 4,
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.875rem',
                height: '40px',
                borderRadius: '4px',
              },
            },
            cancelButtonProps: {
              variant: 'outlined',
              color: 'inherit',
              size: 'large',
              sx: {
                px: 4,
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.875rem',
                height: '40px',
                borderRadius: '4px',
                borderColor: 'rgba(0, 0, 0, 0.23)',
                color: 'rgba(0, 0, 0, 0.87)',
              },
            },
          }}
          submitButtonText={mode === 'edit' ? 'Update' : 'Create'}
          cancelButtonText='Cancel'
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};

export default PersonaFormModal;
