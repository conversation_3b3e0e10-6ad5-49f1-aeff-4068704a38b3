import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Alert,
  Collapse,
  Grid,
  TextField,
  Menu,
  MenuItem,
  Slider,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  VolumeUp as VolumeUpIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  Description as DescriptionIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../../services/base';

const CallLogs = () => {
  const [calls, setCalls] = useState([]);
  const [filteredCalls, setFilteredCalls] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCall, setSelectedCall] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  const [expandedRow, setExpandedRow] = useState(null);
  const [detailedCalls, setDetailedCalls] = useState({});
  const [responsesOpen, setResponsesOpen] = useState(false);
  const [playingAudioId, setPlayingAudioId] = useState(null);
  const audioRef = useRef(null);
  const [audioModalOpen, setAudioModalOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedAudio, setSelectedAudio] = useState(null);
  const [isAudioLoading, setIsAudioLoading] = useState(false);
  const [fromNumberFilter, setFromNumberFilter] = useState('');
  const [toNumberFilter, setToNumberFilter] = useState('');

  const navigate = useNavigate();

  useEffect(() => {
    fetchCalls();
  }, [page, rowsPerPage, sortField, sortDirection]);

  useEffect(() => {
    applyFilters();
  }, [fromNumberFilter, toNumberFilter, calls]);

  const applyFilters = () => {
    if (!calls.length) return;

    let filtered = [...calls];

    if (fromNumberFilter) {
      filtered = filtered.filter(
        (call) => call.from_number && call.from_number.includes(fromNumberFilter)
      );
    }

    if (toNumberFilter) {
      filtered = filtered.filter(
        (call) => call.to_number && call.to_number.includes(toNumberFilter)
      );
    }

    setFilteredCalls(filtered);
    setTotalCount(filtered.length);
  };

  const fetchCalls = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        sort: sortField,
        order: sortDirection,
      };

      const response = await apiService.get('/voice/api/v1/calls', { params });
      const items = response.items || [];
      setCalls(items);
      setFilteredCalls(items);
      setTotalCount(items.length);
    } catch (error) {
      console.error('Error fetching calls:', error);
      showAlert('Error fetching call data', 'error');
    } finally {
      setLoading(false);
    }
  }, [sortField, sortDirection]);

  const fetchCallDetails = async (callId) => {
    try {
      const response = await apiService.get(`/voice/api/v1/calls/${callId}`);
      setDetailedCalls((prev) => ({
        ...prev,
        [callId]: response,
      }));
    } catch (error) {
      console.error('Error fetching call details:', error);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSortChange = (field) => {
    const isAsc = sortField === field && sortDirection === 'asc';
    setSortDirection(isAsc ? 'desc' : 'asc');
    setSortField(field);
  };

  const toggleRowExpansion = (callId) => {
    if (expandedRow !== callId) {
      fetchCallDetails(callId);
    }
    setExpandedRow(expandedRow === callId ? null : callId);
  };

  const handleMenuOpen = (event, call) => {
    setAnchorEl(event.currentTarget);
    setSelectedCall(call);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleViewDetails = () => {
    setDetailsOpen(true);
    handleMenuClose();
  };

  const handleViewResponses = () => {
    navigate(`/call-responses/${selectedCall.id}`);
    handleMenuClose();
  };

  const showAlert = (message, severity) => {
    setAlert({ show: true, message, severity });
    setTimeout(() => {
      setAlert({ show: false, message: '', severity: 'success' });
    }, 5000);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'in_progress':
        return 'warning';
      case 'queued':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatStatusLabel = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'in_progress':
        return 'In Progress';
      case 'queued':
        return 'Queued';
      default:
        return status?.charAt(0).toUpperCase() + status?.slice(1) || '';
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleRetryCall = async (callId) => {
    try {
      await apiService.post(`/voice/api/v1/calls/${callId}/retry`);
      showAlert('Call retry initiated successfully', 'success');
      // Refresh the calls list after a short delay
      setTimeout(() => {
        fetchCalls();
      }, 1000);
    } catch (error) {
      console.error('Error retrying call:', error);
      showAlert('Failed to retry call', 'error');
    }
  };

  const handleAudioPlay = async (audioUrl, callId) => {
    if (!audioUrl) return;

    // If clicking the same audio that's currently playing, toggle pause/play
    if (playingAudioId === callId) {
      if (isPlaying) {
        audioRef.current?.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current?.play();
        setIsPlaying(true);
      }
      return;
    }

    // If a different audio is selected
    setPlayingAudioId(callId);
    setIsAudioLoading(true);

    // Create a new audio element
    const audio = new Audio(audioUrl);

    // Set up event listeners
    audio.oncanplaythrough = () => {
      setIsAudioLoading(false);
      audio.play().then(() => setIsPlaying(true));
    };

    audio.onerror = (error) => {
      console.error('Error playing audio:', error);
      showAlert('Error playing audio', 'error');
      setIsAudioLoading(false);
      setPlayingAudioId(null);
    };

    audio.onended = () => {
      setIsPlaying(false);
    };

    // Clean up previous audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.src = '';
    }

    audioRef.current = audio;
  };

  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds) || !isFinite(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getAudioDuration = async (url) => {
    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      return audioBuffer.duration;
    } catch (error) {
      console.error('Error getting audio duration:', error);
      return null;
    }
  };

  const loadAudio = async (url) => {
    try {
      // First try to fetch the audio file to check its content type
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');

      if (!contentType?.includes('audio/')) {
        console.warn('Warning: Response may not be an audio file. Content-Type:', contentType);
      }

      // Get duration using Web Audio API
      const duration = await getAudioDuration(url);
      if (duration) {
        setDuration(duration);
      }

      // Create a new audio element
      const audio = new Audio();

      // Add event listeners
      audio.addEventListener('timeupdate', handleTimeUpdate);
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('canplaythrough', () => {
        setIsAudioLoading(false);
      });
      audio.addEventListener('ended', () => setIsPlaying(false));

      // Create a promise to handle the loading
      const loadPromise = new Promise((resolve, reject) => {
        const handleMetadata = () => {
          audio.removeEventListener('loadedmetadata', handleMetadata);
          audio.removeEventListener('error', handleError);
          clearTimeout(timeout);
          resolve();
        };

        const handleError = (event) => {
          audio.removeEventListener('loadedmetadata', handleMetadata);
          audio.removeEventListener('error', handleError);
          clearTimeout(timeout);
          reject(event);
        };

        audio.addEventListener('loadedmetadata', handleMetadata);
        audio.addEventListener('error', handleError);

        // Set a timeout to reject if loading takes too long
        const timeout = setTimeout(() => {
          audio.removeEventListener('loadedmetadata', handleMetadata);
          audio.removeEventListener('error', handleError);
          reject(new Error('Audio loading timed out'));
        }, 10000); // 10 second timeout
      });

      // Set source and start loading
      audio.src = url;
      audio.preload = 'metadata';
      audio.load();

      // Wait for the loading to complete
      await loadPromise;

      return audio;
    } catch (error) {
      console.error('Error loading audio:', error);
      throw error;
    }
  };

  const handleTimeUpdate = () => {
    if (
      audioRef.current &&
      !isNaN(audioRef.current.currentTime) &&
      isFinite(audioRef.current.currentTime)
    ) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      console.log('Audio metadata loaded:', {
        duration: audioRef.current.duration,
        readyState: audioRef.current.readyState,
        networkState: audioRef.current.networkState,
      });

      // Try to get duration
      const duration = audioRef.current.duration;
      if (!isNaN(duration) && isFinite(duration) && duration > 0) {
        console.log('Setting duration from metadata:', duration);
        setDuration(duration);
      }
    }
  };

  const handleDownload = async () => {
    if (selectedAudio?.url) {
      try {
        setIsAudioLoading(true);
        // Fetch the audio file
        const response = await fetch(selectedAudio.url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Get the file as a blob
        const blob = await response.blob();

        // Create a URL for the blob
        const blobUrl = window.URL.createObjectURL(blob);

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `call-recording-${selectedAudio.id}.mp3`;

        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        window.URL.revokeObjectURL(blobUrl);

        showAlert('Download started', 'success');
      } catch (error) {
        console.error('Error downloading audio:', error);
        showAlert('Failed to download audio file', 'error');
      } finally {
        setIsAudioLoading(false);
      }
    }
  };

  const handleAudioError = (event) => {
    // Get the audio element that triggered the error
    const audioElement = event.target || event.currentTarget;

    console.error('Audio error details:', {
      event: event,
      audioElement: audioElement,
      url: selectedAudio?.url,
      readyState: audioElement?.readyState,
      networkState: audioElement?.networkState,
      errorCode: audioElement?.error?.code,
      errorMessage: audioElement?.error?.message,
      error: audioElement?.error,
    });

    let errorMessage = 'Error loading audio. ';

    if (audioElement?.error) {
      switch (audioElement.error.code) {
        case 1:
          errorMessage += 'The audio loading was aborted.';
          break;
        case 2:
          errorMessage += 'Network error occurred. Please check your connection.';
          break;
        case 3:
          errorMessage += 'Audio decoding failed. The file may be corrupted.';
          break;
        case 4:
          errorMessage +=
            'Audio format not supported or file not accessible. Please check if the file is a valid audio file.';
          break;
        default:
          errorMessage += 'Unknown error occurred.';
      }
    } else {
      errorMessage +=
        'Unable to load audio file. Please check if the URL is correct and accessible.';
    }

    showAlert(errorMessage, 'error');
    setAudioModalOpen(false);
    setIsAudioLoading(false);
  };

  // Handle audio playback when modal is opened or audio source changes
  useEffect(() => {
    if (audioModalOpen && selectedAudio?.url) {
      // Clean up any existing audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }

      // Reset state
      setIsAudioLoading(true);
      setCurrentTime(0);
      setDuration(0);
      setIsPlaying(false);

      // Load the new audio
      loadAudio(selectedAudio.url)
        .then((audio) => {
          audioRef.current = audio;

          // Add event listeners
          audio.addEventListener('error', handleAudioError);

          // Try to get duration after successful load
          if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
            setDuration(audio.duration);
          }
        })
        .catch((error) => {
          console.error('Failed to load audio:', error);
          handleAudioError(error);
        })
        .finally(() => {
          setIsAudioLoading(false);
        });

      return () => {
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.src = '';
          audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);
          audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
          audioRef.current.removeEventListener('canplaythrough', () => {
            setIsAudioLoading(false);
          });
          audioRef.current.removeEventListener('ended', () => setIsPlaying(false));
          audioRef.current.removeEventListener('error', handleAudioError);
        }
      };
    }
  }, [audioModalOpen, selectedAudio]);

  const handlePlayPause = async () => {
    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        // Ensure audio is loaded before playing
        if (audioRef.current.readyState < 2) {
          await new Promise((resolve) => {
            audioRef.current.addEventListener('canplay', resolve, { once: true });
          });
        }

        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      showAlert('Error playing audio. Please try again.', 'error');
      setIsPlaying(false);
    }
  };

  const handleSliderChange = (event, newValue) => {
    if (audioRef.current && !isNaN(newValue) && isFinite(newValue)) {
      try {
        audioRef.current.currentTime = newValue;
        setCurrentTime(newValue);
      } catch (error) {
        console.error('Error setting audio time:', error);
      }
    }
  };

  return (
    <Container maxWidth='xl'>
      <Box sx={{ mb: 4 }}>
        <Typography variant='h4' gutterBottom>
          Call Logs
        </Typography>
        <Typography variant='body1' color='text.secondary'>
          View and manage your call history
        </Typography>
      </Box>

      {alert.show && (
        <Alert
          severity={alert.severity}
          sx={{ mb: 2 }}
          onClose={() => setAlert({ ...alert, show: false })}
        >
          {alert.message}
        </Alert>
      )}

      <Paper sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2} alignItems='center' justifyContent='space-between'>
            <Box sx={{ display: 'flex', gap: 2, flex: 1, maxWidth: '800px' }}>
              <Grid item xs={12} sm={5}>
                <TextField
                  label='From Number'
                  variant='outlined'
                  size='small'
                  fullWidth
                  value={fromNumberFilter}
                  onChange={(e) => setFromNumberFilter(e.target.value)}
                  placeholder='Filter by from number'
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  label='To Number'
                  variant='outlined'
                  size='small'
                  fullWidth
                  value={toNumberFilter}
                  onChange={(e) => setToNumberFilter(e.target.value)}
                  placeholder='Filter by to number'
                />
              </Grid>
            </Box>
            <Grid item>
              <Button
                variant='outlined'
                size='small'
                onClick={() => {
                  setFromNumberFilter('');
                  setToNumberFilter('');
                }}
                sx={{ mr: 1 }}
              >
                Clear Filters
              </Button>
              <Button
                variant='outlined'
                size='small'
                startIcon={<RefreshIcon />}
                onClick={fetchCalls}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell width='40px'></TableCell>
                <TableCell>
                  <Box
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSortChange('created_at')}
                  >
                    Date/Time
                    {sortField === 'created_at' && (
                      <Box component='span' sx={{ ml: 0.5 }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </Box>
                    )}
                  </Box>
                </TableCell>
                <TableCell>From</TableCell>
                <TableCell>To</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Duration</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Unique ID</TableCell>
                <TableCell>Audio</TableCell>
                <TableCell>Summary</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} align='center'>
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : filteredCalls.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} align='center'>
                    No calls found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredCalls.map((call) => (
                  <React.Fragment key={call.id}>
                    <TableRow
                      hover
                      onClick={() => toggleRowExpansion(call.id)}
                      sx={{
                        cursor: 'pointer',
                        '&.Mui-selected': { backgroundColor: 'action.selected' },
                      }}
                    >
                      <TableCell>
                        <IconButton size='small'>
                          {expandedRow === call.id ? (
                            <KeyboardArrowUpIcon />
                          ) : (
                            <KeyboardArrowDownIcon />
                          )}
                        </IconButton>
                      </TableCell>
                      <TableCell>{new Date(call.created_at).toLocaleString()}</TableCell>
                      <TableCell>{call.from_number ? call.from_number : 'N/A'}</TableCell>
                      <TableCell>{call.to_number}</TableCell>
                      <TableCell>{call.persona_name}</TableCell>
                      <TableCell>{formatDuration(call.duration_seconds)}</TableCell>
                      <TableCell>
                        <Chip
                          label={formatStatusLabel(call.status)}
                          size='small'
                          color={getStatusColor(call.status)}
                        />
                      </TableCell>
                      <TableCell>{call.unique_id ? call.unique_id : 'N/A'}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {isAudioLoading && playingAudioId === call.id ? (
                            <CircularProgress size={24} />
                          ) : (
                            <IconButton
                              size='small'
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAudioPlay(detailedCalls[call.id]?.audio_url, call.id);
                              }}
                              color={
                                playingAudioId === call.id && isPlaying ? 'primary' : 'default'
                              }
                              disabled={!detailedCalls[call.id]?.audio_url}
                              aria-label={
                                playingAudioId === call.id && isPlaying ? 'Pause' : 'Play'
                              }
                            >
                              {playingAudioId === call.id && isPlaying ? (
                                <PauseIcon />
                              ) : (
                                <VolumeUpIcon />
                              )}
                            </IconButton>
                          )}
                          <Typography variant='caption' sx={{ ml: 1 }}>
                            {playingAudioId === call.id && isPlaying ? 'Playing' : 'Play'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{call.summary ? call.summary : 'N/A'}</TableCell>
                      <TableCell>
                        <IconButton
                          size='small'
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMenuOpen(e, call);
                          }}
                        >
                          <MoreVertIcon fontSize='small' />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                    {/* Expanded row for call details */}
                    <TableRow>
                      <TableCell
                        style={{ paddingBottom: 0, paddingTop: 0, border: 0 }}
                        colSpan={10}
                      >
                        <Collapse in={expandedRow === call.id} timeout='auto' unmountOnExit>
                          <Box sx={{ margin: 3 }}>
                            <Grid container spacing={6}>
                              <Grid item xs={12} sm={6} md={3} sx={{ px: 3 }}>
                                <Typography variant='subtitle2' gutterBottom>
                                  Call Details
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Call ID: {call.id}
                                </Typography>
                                <Typography
                                  variant='body2'
                                  color='text.secondary'
                                  sx={{ mt: 1.5, mb: 1.5 }}
                                >
                                  From: {call.from_number}
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Type: {call.call_type || 'Outbound'}
                                </Typography>
                                {detailedCalls[call.id]?.failure_reason && (
                                  <Typography variant='body2' color='error'>
                                    Error: {detailedCalls[call.id].failure_reason}
                                  </Typography>
                                )}
                              </Grid>
                              <Grid item xs={12} sm={6} md={3} sx={{ mb: 2, px: 3 }}>
                                <Typography variant='subtitle2' gutterBottom>
                                  Persona
                                </Typography>
                                <Typography variant='body2'>
                                  {detailedCalls[call.id]?.persona?.name || 'Default Persona'}
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Engine:{' '}
                                  {detailedCalls[call.id]?.persona?.version?.engine || 'Default'}
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Voice:{' '}
                                  {detailedCalls[call.id]?.persona?.version?.voice_type ||
                                    'Default'}
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Language:{' '}
                                  {detailedCalls[call.id]?.persona?.version?.language || 'English'}
                                </Typography>
                              </Grid>
                              <Grid item xs={12} sm={6} md={3} sx={{ mb: 2, px: 3 }}>
                                <Typography variant='subtitle2' gutterBottom>
                                  Prompt
                                </Typography>
                                <Typography variant='body2'>
                                  {detailedCalls[call.id]?.prompt?.type || 'Default'}
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Model:{' '}
                                  {detailedCalls[call.id]?.prompt?.version?.model || 'Default'}
                                </Typography>
                                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                                  Version:{' '}
                                  {detailedCalls[call.id]?.prompt?.version?.version || '1.0'}
                                </Typography>
                              </Grid>
                              <Grid item xs={12} sm={6} md={3} sx={{ mb: 2, px: 3 }}>
                                <Typography variant='subtitle2' gutterBottom>
                                  Media
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                                  {call.status === 'completed' ? (
                                    <>
                                      <Button
                                        size='small'
                                        variant='outlined'
                                        startIcon={<InfoIcon />}
                                        onClick={() => {
                                          setSelectedCall(detailedCalls[call.id] || call);
                                          setDetailsOpen(true);
                                        }}
                                      >
                                        View More Info
                                      </Button>
                                    </>
                                  ) : (
                                    <Typography variant='body2' color='text.secondary'>
                                      {call.status === 'failed'
                                        ? 'Call failed'
                                        : 'Call in progress'}
                                    </Typography>
                                  )}
                                </Box>
                              </Grid>
                            </Grid>

                            {call.error_message && (
                              <Alert severity='error' sx={{ mt: 2 }}>
                                Error: {call.error_message}
                              </Alert>
                            )}

                            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                              {call.status === 'completed' && (
                                <Button
                                  size='small'
                                  variant='outlined'
                                  onClick={() => {
                                    setSelectedCall(detailedCalls[call.id] || call);
                                    setResponsesOpen(true);
                                  }}
                                >
                                  View Responses
                                </Button>
                              )}
                              {call.status === 'failed' && (
                                <Button
                                  size='small'
                                  variant='contained'
                                  color='primary'
                                  startIcon={<RefreshIcon />}
                                  onClick={() => handleRetryCall(call.id)}
                                >
                                  Retry Call
                                </Button>
                              )}
                            </Box>
                          </Box>
                        </Collapse>
                      </TableCell>
                    </TableRow>
                  </React.Fragment>
                ))
              )}
            </TableBody>
          </Table>
          <TablePagination
            rowsPerPageOptions={[10, 25, 50]}
            component='div'
            count={totalCount}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </TableContainer>
      </Paper>

      {/* Call Details Dialog */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>
          Call Details
          <IconButton
            aria-label='close'
            onClick={() => setDetailsOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedCall && (
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant='subtitle2' gutterBottom>
                  Call Information
                </Typography>
                <Typography variant='body2'>
                  <strong>ID:</strong> {selectedCall.id}
                </Typography>
                <Typography variant='body2'>
                  <strong>From:</strong> {selectedCall.from_number}
                </Typography>
                <Typography variant='body2'>
                  <strong>To:</strong> {selectedCall.to_number}
                </Typography>
                <Typography variant='body2'>
                  <strong>Type:</strong> {selectedCall.call_type || 'Outbound'}
                </Typography>
                <Typography variant='body2'>
                  <strong>Duration:</strong> {formatDuration(selectedCall.duration_seconds)}
                </Typography>
                <Typography variant='body2'>
                  <strong>Date/Time:</strong> {new Date(selectedCall.created_at).toLocaleString()}
                </Typography>
                <Typography variant='body2'>
                  <strong>Status:</strong>{' '}
                  <Chip
                    label={formatStatusLabel(selectedCall.status)}
                    size='small'
                    color={getStatusColor(selectedCall.status)}
                  />
                </Typography>
                {selectedCall.failure_reason && (
                  <Typography variant='body2' color='error' sx={{ mt: 1 }}>
                    <strong>Error:</strong> {selectedCall.failure_reason}
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant='subtitle2' gutterBottom>
                  Variables
                </Typography>
                <Paper variant='outlined' sx={{ p: 2 }}>
                  {selectedCall.variables ? (
                    Object.entries(selectedCall.variables).map(([key, value]) => (
                      <Typography key={key} variant='body2'>
                        <strong>{key}:</strong> {value}
                      </Typography>
                    ))
                  ) : (
                    <Typography variant='body2' color='text.secondary'>
                      No variables set
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Responses Dialog */}
      <Dialog open={responsesOpen} onClose={() => setResponsesOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>
          Call Responses
          <IconButton
            aria-label='close'
            onClick={() => setResponsesOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedCall && (
            <Box sx={{ mb: 2 }}>
              <Typography variant='subtitle2' gutterBottom>
                Call Information
              </Typography>
              <Typography variant='body2'>
                <strong>To:</strong> {selectedCall.to_number}
              </Typography>
              <Typography variant='body2'>
                <strong>Date/Time:</strong> {new Date(selectedCall.created_at).toLocaleString()}
              </Typography>
            </Box>
          )}

          <Typography variant='subtitle2' gutterBottom>
            Responses
          </Typography>
          <Paper variant='outlined' sx={{ p: 2 }}>
            {selectedCall?.responses && selectedCall.responses.length > 0 ? (
              <Box component='form' sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {selectedCall.responses.map((response, index) => (
                  <Box key={index} sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography variant='body2' color='primary'>
                      <strong>Q:</strong> {response.question_text}
                    </Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={2}
                      variant='outlined'
                      size='small'
                      label='Answer'
                      value={response.answer_text || ''}
                      onChange={(e) => {
                        const updatedResponses = [...selectedCall.responses];
                        updatedResponses[index] = {
                          ...response,
                          answer_text: e.target.value,
                        };
                        setSelectedCall({
                          ...selectedCall,
                          responses: updatedResponses,
                        });
                      }}
                    />
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography variant='body2' color='text.secondary'>
                No responses recorded
              </Typography>
            )}
          </Paper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResponsesOpen(false)}>Cancel</Button>
          <Button
            variant='contained'
            color='primary'
            onClick={async () => {
              try {
                const updates = selectedCall.responses.map((response) => ({
                  response_id: response.id,
                  answer_text: response.answer_text || '',
                }));

                await apiService.patch(`/voice/api/v1/calls/${selectedCall.id}/responses`, {
                  updates: updates,
                });
                showAlert('Responses updated successfully', 'success');
                setResponsesOpen(false);
                // Refresh both the calls list and the specific call details
                await fetchCalls();
                await fetchCallDetails(selectedCall.id);
              } catch (error) {
                console.error('Error updating responses:', error);
                showAlert('Failed to update responses', 'error');
              }
            }}
          >
            Save Responses
          </Button>
        </DialogActions>
      </Dialog>

      {/* Audio Player Modal */}
      <Dialog
        open={audioModalOpen}
        onClose={() => {
          setAudioModalOpen(false);
          if (audioRef.current) {
            audioRef.current.pause();
            setIsPlaying(false);
          }
        }}
        maxWidth='sm'
        fullWidth
      >
        <DialogTitle>
          Call Recording
          <IconButton
            aria-label='close'
            onClick={() => {
              setAudioModalOpen(false);
              if (audioRef.current) {
                audioRef.current.pause();
                setIsPlaying(false);
              }
            }}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <IconButton
                onClick={handlePlayPause}
                size='large'
                disabled={!selectedAudio?.url || isAudioLoading}
                sx={{
                  color: 'success.main',
                  '&:hover': {
                    backgroundColor: 'success.light',
                    color: 'success.contrastText',
                  },
                }}
              >
                {isAudioLoading ? (
                  <CircularProgress size={24} color='success' />
                ) : isPlaying ? (
                  <PauseIcon />
                ) : (
                  <PlayArrowIcon />
                )}
              </IconButton>
              <Box sx={{ flex: 1, mx: 2 }}>
                <Slider
                  value={currentTime}
                  max={duration || 100}
                  onChange={handleSliderChange}
                  aria-labelledby='audio-slider'
                  disabled={!selectedAudio?.url || isAudioLoading}
                  sx={{
                    color: 'success.main',
                    '& .MuiSlider-thumb': {
                      '&:hover, &.Mui-focusVisible': {
                        boxShadow: '0px 0px 0px 8px rgba(76, 175, 80, 0.16)',
                      },
                    },
                  }}
                />
              </Box>
              <Typography variant='body2' sx={{ minWidth: 100 }}>
                {formatTime(currentTime)} / {formatTime(duration)}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                startIcon={<DownloadIcon />}
                onClick={handleDownload}
                variant='outlined'
                size='small'
                disabled={!selectedAudio?.url || isAudioLoading}
                sx={{
                  color: 'success.main',
                  borderColor: 'success.main',
                  '&:hover': {
                    borderColor: 'success.dark',
                    backgroundColor: 'success.light',
                    color: 'success.dark',
                  },
                }}
              >
                Download Recording
              </Button>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>

      {/* Action Menu */}
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        {selectedCall?.status === 'completed' && (
          <>
            <MenuItem
              onClick={() => {
                setSelectedCall(detailedCalls[selectedCall.id] || selectedCall);
                setDetailsOpen(true);
                handleMenuClose();
              }}
            >
              <InfoIcon fontSize='small' sx={{ mr: 1 }} />
              View More Info
            </MenuItem>
            <MenuItem
              onClick={() => {
                setSelectedCall(detailedCalls[selectedCall.id] || selectedCall);
                setResponsesOpen(true);
                handleMenuClose();
              }}
            >
              <DescriptionIcon fontSize='small' sx={{ mr: 1 }} />
              View Responses
            </MenuItem>
          </>
        )}
        {selectedCall?.status === 'failed' && (
          <MenuItem
            onClick={() => {
              handleRetryCall(selectedCall.id);
              handleMenuClose();
            }}
          >
            <RefreshIcon fontSize='small' sx={{ mr: 1 }} />
            Retry Call
          </MenuItem>
        )}
      </Menu>
    </Container>
  );
};

export default CallLogs;
