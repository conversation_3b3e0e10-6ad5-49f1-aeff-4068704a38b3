import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  FormControl,
  Select,
  InputLabel,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useAuth } from '../../contexts/AuthContext';
import { useProject } from '../../contexts/ProjectContext';
import { useState } from 'react';

/**
 * Application header component with hamburger menu and user profile
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether sidebar is open
 * @param {Function} props.handleDrawerToggle - Function to toggle sidebar
 * @returns {JSX.Element} AppHeader component
 */
const AppHeader = ({ open, handleDrawerToggle }) => {
  const { user, logout } = useAuth();
  const { projects, selectedProjectId, setSelectedProjectId, loading, refreshProjects } =
    useProject();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    logout();
  };

  const handleProjectChange = (event) => {
    setSelectedProjectId(event.target.value);
    // Refresh data when project changes
    refreshProjects();
  };

  return (
    <AppBar
      position='fixed'
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        boxShadow: 1,
        backgroundColor: 'white',
        color: 'primary.main',
      }}
    >
      <Toolbar>
        <IconButton
          color='inherit'
          aria-label='toggle drawer'
          edge='start'
          onClick={handleDrawerToggle}
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>

        <Typography variant='h6' component='div' sx={{ mr: 4, fontSize: '1.1rem' }}>
          Vegrow Voice
        </Typography>

        <FormControl sx={{ minWidth: 200, mr: 'auto', ml: 6 }}>
          <InputLabel id='project-select-label'>Select Project</InputLabel>
          <Select
            labelId='project-select-label'
            id='project-select'
            value={selectedProjectId}
            label='Select Project'
            onChange={handleProjectChange}
            disabled={loading || projects.length === 0}
            size='small'
            sx={{ fontSize: '15px' }}
          >
            {projects.map((project) => (
              <MenuItem key={project.id} value={project.id}>
                {project.title}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {user && (
          <Box>
            <Tooltip title='Account settings'>
              <IconButton onClick={handleMenu} sx={{ p: 0 }}>
                <Avatar
                  alt={user.name || 'User'}
                  src={user.picture}
                  sx={{ width: 40, height: 40 }}
                />
              </IconButton>
            </Tooltip>

            <Menu
              id='menu-appbar'
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <MenuItem>
                <Typography>{user.name}</Typography>
              </MenuItem>
              <MenuItem>
                <Typography variant='body2' color='textSecondary'>
                  {user.email}
                </Typography>
              </MenuItem>
              <MenuItem onClick={handleLogout}>Logout</MenuItem>
            </Menu>
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
