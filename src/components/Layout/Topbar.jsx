import React, { useState, useEffect } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  FormControl,
  Select,
  MenuItem,
  Box,
  CircularProgress,
  Avatar,
  IconButton,
  Menu,
  Tooltip,
  InputLabel,
  Badge,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { useAuth } from '../../contexts/AuthContext';

const ProjectSelect = styled(FormControl)(({ theme }) => ({
  minWidth: 180,
  marginLeft: theme.spacing(2),
  marginRight: theme.spacing(2),
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#fff',
    borderRadius: theme.shape.borderRadius,
  },
}));

const Topbar = () => {
  const { user, logout } = useAuth();
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('');
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  // Handle menu open/close
  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
  };

  // Fetch projects on component mount
  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `${process.env.REACT_APP_API_URL || 'http://localhost:7004'}/voice/api/v1/projects`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          setProjects(data.projects || []);

          // Set first project as selected by default if no project is selected
          if (data.projects && data.projects.length > 0 && !selectedProject) {
            setSelectedProject(data.projects[0].id.toString());
            // Store selected project in localStorage
            localStorage.setItem('selectedProject', data.projects[0].id.toString());
          }
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();

    // Load selected project from localStorage if available
    const savedProject = localStorage.getItem('selectedProject');
    if (savedProject) {
      setSelectedProject(savedProject);
    }
  }, []);

  const handleProjectChange = (event) => {
    const projectId = event.target.value;
    setSelectedProject(projectId);
    localStorage.setItem('selectedProject', projectId);
    // Optionally dispatch an event or use context to notify other components
    window.dispatchEvent(new CustomEvent('projectChanged', { detail: { projectId } }));
  };

  return (
    <AppBar position='fixed' color='default' elevation={1}>
      <Toolbar>
        <Typography variant='h6' component='div' sx={{ flexGrow: 0, fontWeight: 'bold' }}>
          Vegrow Voice
        </Typography>

        {loading ? (
          <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
            <CircularProgress size={20} />
          </Box>
        ) : (
          <ProjectSelect variant='outlined' size='small'>
            <InputLabel id='project-select-label'>Project</InputLabel>
            <Select
              labelId='project-select-label'
              id='project-select'
              value={selectedProject}
              onChange={handleProjectChange}
              label='Project'
            >
              {projects.map((project) => (
                <MenuItem key={project.id} value={project.id.toString()}>
                  {project.title} - {project.language}
                </MenuItem>
              ))}
            </Select>
          </ProjectSelect>
        )}

        <Box sx={{ flexGrow: 1 }} />

        <Box sx={{ display: 'flex' }}>
          <Tooltip title='Notifications'>
            <IconButton color='inherit'>
              <Badge badgeContent={3} color='error'>
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          <Tooltip title='Account'>
            <IconButton onClick={handleMenu} color='inherit'>
              {user?.picture ? (
                <Avatar src={user.picture} alt={user.name} sx={{ width: 32, height: 32 }} />
              ) : (
                <AccountCircleIcon />
              )}
            </IconButton>
          </Tooltip>
          <Menu
            id='menu-appbar'
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleClose}>Profile</MenuItem>
            <MenuItem onClick={handleClose}>My account</MenuItem>
            <MenuItem onClick={handleLogout}>Logout</MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Topbar;
