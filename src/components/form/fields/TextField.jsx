import React from 'react';
import { TextField as MuiTextField, FormHelperText } from '@mui/material';
import { useField } from 'formik';

/**
 * TextField component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const TextField = ({ label, helperText, required, ...props }) => {
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);

  return (
    <>
      <MuiTextField
        {...field}
        {...props}
        label={required ? `${label} *` : label}
        error={isError}
        helperText={isError ? meta.error : helperText}
        fullWidth
        variant='outlined'
        margin='normal'
      />
      {!isError && helperText && <FormHelperText>{helperText}</FormHelperText>}
    </>
  );
};

export default TextField;
