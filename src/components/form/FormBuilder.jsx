import React from 'react';
import { Formik, Form } from 'formik';
import ConditionalWrapper from './ConditionalWrapper';
import { <PERSON>rid, Button, Box, Typography, Divider, useTheme } from '@mui/material';
import { createValidationSchema } from './validation/validationRules';
import {
  TextField,
  SelectField,
  CheckboxField,
  RadioGroupField,
  DatePickerField,
  FileUploadField,
  MultiSelectField,
  SwitchField,
} from './fields';

/**
 * FormBuilder - A component to dynamically generate forms based on field definitions
 *
 * @param {Object} props - Component properties
 * @param {Array} props.fields - Array of field definition objects
 * @param {Object} props.initialValues - Initial values for the form
 * @param {Function} props.onSubmit - Function to handle form submission
 * @param {String} props.title - Optional form title
 * @param {String} props.submitButtonText - Text for the submit button
 * @param {String} props.cancelButtonText - Text for the cancel button
 * @param {Function} props.onCancel - Function to handle form cancellation
 * @param {Object} props.formLayout - Form layout configuration (grid spacing, etc.)
 * @returns {JSX.Element}
 */
const FormBuilder = ({
  fields,
  initialValues = {},
  onSubmit,
  title,
  submitButtonText = 'Submit',
  cancelButtonText = 'Cancel',
  onCancel,
  formLayout = {
    spacing: 2,
    direction: 'row',
    containerProps: {},
    submitButtonProps: {},
    cancelButtonProps: {},
    elevation: 0,
    variant: 'outlined',
    padding: 3,
    fieldSpacing: 2,
  },
}) => {
  const theme = useTheme();
  // Create validation schema from field definitions
  const validationSchema = createValidationSchema(fields);

  // Create default initial values if not provided
  const defaultInitialValues = {};
  fields.forEach((field) => {
    if (!(field.name in initialValues)) {
      switch (field.type) {
        case 'checkbox':
          defaultInitialValues[field.name] = false;
          break;
        case 'array':
          defaultInitialValues[field.name] = [];
          break;
        case 'number':
          defaultInitialValues[field.name] = '';
          break;
        default:
          defaultInitialValues[field.name] = '';
      }
    }
  });

  const finalInitialValues = { ...defaultInitialValues, ...initialValues };

  // Render a specific field based on its type
  const renderField = (field) => {
    const { type, name, label, helperText, required, options, ...restProps } = field;

    const commonProps = {
      name,
      label: undefined, // Remove label
      placeholder: required ? `${label || name} *` : label || name, // Add placeholder with asterisk for required fields
      helperText,
      required: false, // We handle required indicator in the placeholder
      fullWidth: true,
      margin: 'none',
      size: field.size || 'medium',
      InputLabelProps: {
        shrink: false,
      },
      InputProps: {
        sx: {
          '& .MuiInputBase-input': {
            fontSize: '0.875rem',
            height: '40px',
            padding: '8px 12px',
            '&::placeholder': {
              opacity: 1,
              color: '#9e9e9e',
              fontSize: '0.875rem',
              fontWeight: 400,
            },
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderWidth: '1px',
            borderColor: 'rgba(0, 0, 0, 0.23)',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.23)',
          },
        },
      },
      FormHelperTextProps: {
        sx: {
          mt: 0.5,
          mx: 0,
          fontSize: '0.75rem',
          lineHeight: 1.5,
          color: '#9e9e9e',
        },
      },
      ...restProps,
    };

    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'tel':
        return <TextField type={type} {...commonProps} />;
      case 'select':
        return <SelectField options={options || []} {...commonProps} />;
      case 'multiselect':
        return <MultiSelectField options={options || []} {...commonProps} />;
      case 'checkbox':
        return <CheckboxField {...commonProps} />;
      case 'switch':
        return <SwitchField {...commonProps} />;
      case 'radio':
        return <RadioGroupField options={options || []} {...commonProps} />;
      case 'date':
        return <DatePickerField {...commonProps} />;
      case 'file':
        return <FileUploadField {...commonProps} />;
      default:
        return <TextField {...commonProps} />;
    }
  };

  const formContainerStyles = {
    p: formLayout.padding,
    ...(formLayout.variant === 'outlined' && {
      border: `1px solid ${theme.palette.divider}`,
      borderRadius: theme.shape.borderRadius,
    }),
    ...formLayout.containerProps?.sx,
  };

  return (
    <Box {...formLayout.containerProps} sx={formContainerStyles}>
      {title && (
        <>
          <Typography variant='h5' component='h2' gutterBottom>
            {title}
          </Typography>
          <Divider sx={{ mb: 3 }} />
        </>
      )}

      <Formik
        initialValues={finalInitialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ isSubmitting, isValid, dirty }) => (
          <Form>
            <Grid
              container
              spacing={formLayout.fieldSpacing || 2}
              direction={formLayout.direction}
              sx={{ '& .MuiGrid-item': { paddingTop: '0 !important' } }}
            >
              {fields.map((field) => {
                // Wrap field in ConditionalWrapper if it has conditional property
                const fieldComponent = renderField(field);

                return (
                  <Grid
                    item
                    key={field.name}
                    xs={field.gridProps?.xs || 12}
                    md={field.gridProps?.md || 12}
                    sm={field.gridProps?.sm || 12}
                    lg={field.gridProps?.lg || 12}
                    xl={field.gridProps?.xl || 12}
                    sx={field.gridProps?.sx || {}}
                  >
                    {field.conditional ? (
                      <ConditionalWrapper condition={field.conditional}>
                        {fieldComponent}
                      </ConditionalWrapper>
                    ) : (
                      fieldComponent
                    )}
                  </Grid>
                );
              })}

              <Grid item xs={12}>
                <Box
                  sx={{
                    mt: 3,
                    display: 'flex',
                    justifyContent: formLayout.buttonAlignment || 'flex-end',
                    gap: 2,
                  }}
                >
                  {onCancel && (
                    <Button
                      variant={formLayout.cancelButtonProps?.variant || 'outlined'}
                      color={formLayout.cancelButtonProps?.color || 'secondary'}
                      onClick={onCancel}
                      disabled={isSubmitting}
                      size={formLayout.cancelButtonProps?.size || 'medium'}
                      {...formLayout.cancelButtonProps}
                    >
                      {cancelButtonText}
                    </Button>
                  )}
                  <Button
                    type='submit'
                    variant={formLayout.submitButtonProps?.variant || 'contained'}
                    color={formLayout.submitButtonProps?.color || 'primary'}
                    disabled={isSubmitting || !(isValid && dirty)}
                    size={formLayout.submitButtonProps?.size || 'medium'}
                    {...formLayout.submitButtonProps}
                  >
                    {submitButtonText}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Form>
        )}
      </Formik>
    </Box>
  );
};

export default FormBuilder;
